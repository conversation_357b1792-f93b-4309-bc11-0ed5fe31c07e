{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.13", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@tanstack/react-query": "^5.75.4", "@tanstack/react-query-devtools": "^5.75.4", "@tanstack/react-table": "^8.21.3", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "lucide-react": "^0.507.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.5.3", "recharts": "^2.15.3", "sonner": "^2.0.3", "stripe": "^18.1.1", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.25.0", "@shadcn/ui": "^0.0.4", "@tailwindcss/postcss": "^4.1.5", "@tailwindcss/vite": "^4.1.5", "@types/node": "^22.15.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "shadcn": "^2.5.0", "tailwindcss": "^4.1.5", "tw-animate-css": "^1.2.9", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}